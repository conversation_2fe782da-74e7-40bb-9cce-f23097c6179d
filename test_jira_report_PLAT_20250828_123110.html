
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JIRA Processing Report - PLAT</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        
        .status-green { background-color: #d4edda; color: #155724; }
        .status-amber { background-color: #fff3cd; color: #856404; }
        .status-red { background-color: #f8d7da; color: #721c24; }
        .metric-card { border-left: 4px solid #007bff; }
        .chart-container { min-height: 400px; }
        .nav-pills .nav-link.active { background-color: #007bff; }
        .table-responsive { max-height: 500px; overflow-y: auto; }
        
    </style>
</head>
<body>
    <div class="container-fluid">
        
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h1 class="mb-0">JIRA Processing Report - PLAT</h1>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Report Generated:</strong><br>
                                2025-08-28 12:31:10
                            </div>
                            <div class="col-md-3">
                                <strong>Processing Duration:</strong><br>
                                0.00 seconds
                            </div>
                            <div class="col-md-3">
                                <strong>Producers:</strong><br>
                                3
                            </div>
                            <div class="col-md-3">
                                <strong>Consumers:</strong><br>
                                5
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        
        <div class="row mb-4">
            <div class="col-12">
                <ul class="nav nav-pills" id="reportTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="executive-tab" data-bs-toggle="pill"
                                data-bs-target="#executive" type="button" role="tab">Executive Summary</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="producers-tab" data-bs-toggle="pill"
                                data-bs-target="#producers" type="button" role="tab">Producers</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="consumers-tab" data-bs-toggle="pill"
                                data-bs-target="#consumers" type="button" role="tab">Consumers</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="api-tab" data-bs-toggle="pill"
                                data-bs-target="#api" type="button" role="tab">API Metrics</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="operations-tab" data-bs-toggle="pill"
                                data-bs-target="#operations" type="button" role="tab">Operations</button>
                    </li>
                </ul>
            </div>
        </div>
        
        
        <div class="tab-content" id="reportTabsContent">
            <div class="tab-pane fade show active" id="executive" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">Total Records</h5>
                                <h2 class="text-primary">2,537</h2>
                                <small class="text-muted">Processed vs 2,500 estimated</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">Accuracy</h5>
                                <h2 class="status-red">1.48%</h2>
                                <small class="text-muted">Difference from estimate</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">API Success Rate</h5>
                                <h2 class="text-success">99.7%</h2>
                                <small class="text-muted">3,729 of 3,740 calls</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">Processing Time</h5>
                                <h2 class="text-info">96.8s</h2>
                                <small class="text-muted">Total producer time</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Producer Performance Overview</h5>
                            </div>
                            <div class="card-body">
                                <div id="producerOverviewChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>API Call Distribution</h5>
                            </div>
                            <div class="card-body">
                                <div id="apiDistributionChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
        
            <div class="tab-pane fade" id="producers" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Producer Performance Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Producer</th>
                                                <th>JQL Query</th>
                                                <th>Records Processed</th>
                                                <th>Records Estimate</th>
                                                <th>Accuracy</th>
                                                <th>Processing Time</th>
                                                <th>Waiting Time</th>
                                                <th>CPU Time</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
            <tr>
                <td>producer_0</td>
                <td><small>project = PLAT AND updated >= '2024-01-01' ORDER B...</small></td>
                <td>1,247</td>
                <td>1,200</td>
                <td class="status-red">3.92%</td>
                <td>45.20s</td>
                <td>2.10s</td>
                <td>38.42s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_1</td>
                <td><small>project = PLAT AND created >= '2024-01-15' AND sta...</small></td>
                <td>823</td>
                <td>850</td>
                <td class="status-red">3.18%</td>
                <td>32.70s</td>
                <td>2.60s</td>
                <td>27.80s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_2</td>
                <td><small>project = PLAT AND assignee = currentUser() AND re...</small></td>
                <td>467</td>
                <td>450</td>
                <td class="status-red">3.78%</td>
                <td>18.90s</td>
                <td>3.10s</td>
                <td>16.06s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Producer Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div id="producerTimelineChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
        
            <div class="tab-pane fade" id="consumers" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Consumer Performance Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Consumer</th>
                                                <th>Type</th>
                                                <th>Messages Received</th>
                                                <th>Messages Sent</th>
                                                <th>Processing Time</th>
                                                <th>Total Duration</th>
                                                <th>Scaling Events</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
            <tr>
                <td>consumer_apple</td>
                <td>consume_issues</td>
                <td>2,537</td>
                <td>2,537</td>
                <td>78.50s</td>
                <td>78.50s</td>
                <td>2</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>changelog_processor</td>
                <td>consume_changelog</td>
                <td>1,247</td>
                <td>1,247</td>
                <td>34.20s</td>
                <td>34.20s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>worklog_processor</td>
                <td>consume_worklog</td>
                <td>892</td>
                <td>892</td>
                <td>28.70s</td>
                <td>28.70s</td>
                <td>1</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>comment_processor</td>
                <td>consume_comment</td>
                <td>1,534</td>
                <td>1,534</td>
                <td>41.30s</td>
                <td>41.30s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>issue_links_processor</td>
                <td>consume_issue_links</td>
                <td>678</td>
                <td>678</td>
                <td>19.80s</td>
                <td>19.80s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Consumer Throughput</h5>
                            </div>
                            <div class="card-body">
                                <div id="consumerThroughputChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Scaling Events Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div id="scalingEventsChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
        
            <div class="tab-pane fade" id="api" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>API Call Statistics</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Endpoint</th>
                                                <th>Total Requests</th>
                                                <th>Success (No Retry)</th>
                                                <th>Success (With Retry)</th>
                                                <th>Failed</th>
                                                <th>Success Rate</th>
                                                <th>Retry Rate</th>
                                                <th>Avg Time (Excl. Wait)</th>
                                                <th>Avg Time (Incl. Wait)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
            <tr>
                <td><small>api/3/search/jql</small></td>
                <td>67</td>
                <td>58</td>
                <td>7</td>
                <td>2</td>
                <td>97.0%</td>
                <td>10.4%</td>
                <td>0.245s</td>
                <td>0.312s</td>
            </tr>
            
            <tr>
                <td><small>api/3/issue/{issueKey}/changelog</small></td>
                <td>1,247</td>
                <td>1,198</td>
                <td>45</td>
                <td>4</td>
                <td>99.7%</td>
                <td>3.6%</td>
                <td>0.156s</td>
                <td>0.189s</td>
            </tr>
            
            <tr>
                <td><small>api/3/issue/{issueKey}/worklog</small></td>
                <td>892</td>
                <td>867</td>
                <td>23</td>
                <td>2</td>
                <td>99.8%</td>
                <td>2.6%</td>
                <td>0.134s</td>
                <td>0.167s</td>
            </tr>
            
            <tr>
                <td><small>api/3/issue/{issueKey}/comment</small></td>
                <td>1,534</td>
                <td>1,489</td>
                <td>42</td>
                <td>3</td>
                <td>99.8%</td>
                <td>2.7%</td>
                <td>0.142s</td>
                <td>0.178s</td>
            </tr>
            
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Retry Distribution</h5>
                            </div>
                            <div class="card-body">
                                <div id="retryDistributionChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Error Types</h5>
                            </div>
                            <div class="card-body">
                                <div id="errorTypesChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
        
            <div class="tab-pane fade" id="operations" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>System Resource Usage</h5>
                            </div>
                            <div class="card-body">
                                <div id="systemResourceChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Processing Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div id="processingTimelineChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Detailed Logs and Events</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Timestamp</th>
                                                <th>Component</th>
                                                <th>Event</th>
                                                <th>Details</th>
                                            </tr>
                                        </thead>
                                        <tbody id="operationalLogTable">
                                            <!-- Populated by JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    
    <script>
        
        // Chart data
        const producerData = {"names": ["producer_0", "producer_1", "producer_2"], "records": [1247, 823, 467], "timeline": [{"x": ["2025-08-28T12:16:10.060958", "2025-08-28T12:16:55.260958"], "y": ["producer_0", "producer_0"], "mode": "lines", "line": {"width": 10}, "name": "producer_0", "showlegend": false}, {"x": ["2025-08-28T12:16:15.060958", "2025-08-28T12:16:47.760958"], "y": ["producer_1", "producer_1"], "mode": "lines", "line": {"width": 10}, "name": "producer_1", "showlegend": false}, {"x": ["2025-08-28T12:16:20.060958", "2025-08-28T12:16:38.960958"], "y": ["producer_2", "producer_2"], "mode": "lines", "line": {"width": 10}, "name": "producer_2", "showlegend": false}]};
        const consumerData = {"names": ["consumer_apple", "changelog_processor", "worklog_processor", "comment_processor", "issue_links_processor"], "throughput": [32.318471337579616, 36.461988304093566, 31.080139372822302, 37.142857142857146, 34.24242424242424]};
        const apiData = {"endpoints": ["api/3/search/jql", "api/3/issue/{issueKey}/changelog", "api/3/issue/{issueKey}/worklog", "api/3/issue/{issueKey}/comment"], "requests": [67, 1247, 892, 1534], "retryLabels": [0, 1, 2, 3], "retryCounts": [3612, 98, 19, 11], "errorTypes": ["TIMEOUT", "HTTP_ERROR", "RATE_LIMIT", "CONNECTION_ERROR"], "errorCounts": [2, 2, 4, 3]};

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
        });

        function initializeCharts() {
            // Producer Overview Chart
            const producerOverview = {
                data: [{
                    x: producerData.names,
                    y: producerData.records,
                    type: 'bar',
                    name: 'Records Processed',
                    marker: { color: '#007bff' }
                }],
                layout: {
                    title: 'Records Processed by Producer',
                    xaxis: { title: 'Producer' },
                    yaxis: { title: 'Records' },
                    responsive: true
                }
            };
            Plotly.newPlot('producerOverviewChart', producerOverview.data, producerOverview.layout);

            // API Distribution Chart
            const apiDistribution = {
                data: [{
                    labels: apiData.endpoints,
                    values: apiData.requests,
                    type: 'pie',
                    textinfo: 'label+percent',
                    textposition: 'outside'
                }],
                layout: {
                    title: 'API Requests Distribution',
                    responsive: true
                }
            };
            Plotly.newPlot('apiDistributionChart', apiDistribution.data, apiDistribution.layout);

            // Producer Timeline Chart
            const producerTimeline = {
                data: producerData.timeline,
                layout: {
                    title: 'Producer Processing Timeline',
                    xaxis: { title: 'Time' },
                    yaxis: { title: 'Producer' },
                    responsive: true
                }
            };
            Plotly.newPlot('producerTimelineChart', producerTimeline.data, producerTimeline.layout);

            // Consumer Throughput Chart
            const consumerThroughput = {
                data: [{
                    x: consumerData.names,
                    y: consumerData.throughput,
                    type: 'bar',
                    name: 'Messages/Second',
                    marker: { color: '#28a745' }
                }],
                layout: {
                    title: 'Consumer Throughput',
                    xaxis: { title: 'Consumer' },
                    yaxis: { title: 'Messages/Second' },
                    responsive: true
                }
            };
            Plotly.newPlot('consumerThroughputChart', consumerThroughput.data, consumerThroughput.layout);

            // Retry Distribution Chart
            const retryDistribution = {
                data: [{
                    x: apiData.retryLabels,
                    y: apiData.retryCounts,
                    type: 'bar',
                    name: 'Request Count',
                    marker: { color: '#ffc107' }
                }],
                layout: {
                    title: 'API Retry Distribution',
                    xaxis: { title: 'Retry Count' },
                    yaxis: { title: 'Requests' },
                    responsive: true
                }
            };
            Plotly.newPlot('retryDistributionChart', retryDistribution.data, retryDistribution.layout);

            // Error Types Chart
            if (apiData.errorTypes.length > 0) {
                const errorTypes = {
                    data: [{
                        labels: apiData.errorTypes,
                        values: apiData.errorCounts,
                        type: 'pie',
                        textinfo: 'label+percent'
                    }],
                    layout: {
                        title: 'API Error Types',
                        responsive: true
                    }
                };
                Plotly.newPlot('errorTypesChart', errorTypes.data, errorTypes.layout);
            }
        }
        
    </script>
</body>
</html>
        