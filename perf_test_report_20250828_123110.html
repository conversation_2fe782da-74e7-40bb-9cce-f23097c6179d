
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JIRA Processing Report - PERF_TEST</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        
        .status-green { background-color: #d4edda; color: #155724; }
        .status-amber { background-color: #fff3cd; color: #856404; }
        .status-red { background-color: #f8d7da; color: #721c24; }
        .metric-card { border-left: 4px solid #007bff; }
        .chart-container { min-height: 400px; }
        .nav-pills .nav-link.active { background-color: #007bff; }
        .table-responsive { max-height: 500px; overflow-y: auto; }
        
    </style>
</head>
<body>
    <div class="container-fluid">
        
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h1 class="mb-0">JIRA Processing Report - PERF_TEST</h1>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Report Generated:</strong><br>
                                2025-08-28 12:31:10
                            </div>
                            <div class="col-md-3">
                                <strong>Processing Duration:</strong><br>
                                0.00 seconds
                            </div>
                            <div class="col-md-3">
                                <strong>Producers:</strong><br>
                                50
                            </div>
                            <div class="col-md-3">
                                <strong>Consumers:</strong><br>
                                20
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        
        <div class="row mb-4">
            <div class="col-12">
                <ul class="nav nav-pills" id="reportTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="executive-tab" data-bs-toggle="pill"
                                data-bs-target="#executive" type="button" role="tab">Executive Summary</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="producers-tab" data-bs-toggle="pill"
                                data-bs-target="#producers" type="button" role="tab">Producers</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="consumers-tab" data-bs-toggle="pill"
                                data-bs-target="#consumers" type="button" role="tab">Consumers</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="api-tab" data-bs-toggle="pill"
                                data-bs-target="#api" type="button" role="tab">API Metrics</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="operations-tab" data-bs-toggle="pill"
                                data-bs-target="#operations" type="button" role="tab">Operations</button>
                    </li>
                </ul>
            </div>
        </div>
        
        
        <div class="tab-content" id="reportTabsContent">
            <div class="tab-pane fade show active" id="executive" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">Total Records</h5>
                                <h2 class="text-primary">111,250</h2>
                                <small class="text-muted">Processed vs 105,125 estimated</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">Accuracy</h5>
                                <h2 class="status-red">5.83%</h2>
                                <small class="text-muted">Difference from estimate</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">API Success Rate</h5>
                                <h2 class="text-success">0.0%</h2>
                                <small class="text-muted">0 of 0 calls</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">Processing Time</h5>
                                <h2 class="text-info">3950.0s</h2>
                                <small class="text-muted">Total producer time</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Producer Performance Overview</h5>
                            </div>
                            <div class="card-body">
                                <div id="producerOverviewChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>API Call Distribution</h5>
                            </div>
                            <div class="card-body">
                                <div id="apiDistributionChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
        
            <div class="tab-pane fade" id="producers" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Producer Performance Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Producer</th>
                                                <th>JQL Query</th>
                                                <th>Records Processed</th>
                                                <th>Records Estimate</th>
                                                <th>Accuracy</th>
                                                <th>Processing Time</th>
                                                <th>Waiting Time</th>
                                                <th>CPU Time</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
            <tr>
                <td>producer_0</td>
                <td><small>project = PLAT AND updated >= '2024-01-01'...</small></td>
                <td>1,000</td>
                <td>1,000</td>
                <td class="status-green">0.00%</td>
                <td>30.00s</td>
                <td>1.00s</td>
                <td>28.00s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_1</td>
                <td><small>project = PLAT AND updated >= '2024-01-02'...</small></td>
                <td>1,050</td>
                <td>1,045</td>
                <td class="status-amber">0.48%</td>
                <td>32.00s</td>
                <td>1.10s</td>
                <td>29.80s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_2</td>
                <td><small>project = PLAT AND updated >= '2024-01-03'...</small></td>
                <td>1,100</td>
                <td>1,090</td>
                <td class="status-amber">0.92%</td>
                <td>34.00s</td>
                <td>1.20s</td>
                <td>31.60s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_3</td>
                <td><small>project = PLAT AND updated >= '2024-01-04'...</small></td>
                <td>1,150</td>
                <td>1,135</td>
                <td class="status-red">1.32%</td>
                <td>36.00s</td>
                <td>1.30s</td>
                <td>33.40s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_4</td>
                <td><small>project = PLAT AND updated >= '2024-01-05'...</small></td>
                <td>1,200</td>
                <td>1,180</td>
                <td class="status-red">1.69%</td>
                <td>38.00s</td>
                <td>1.40s</td>
                <td>35.20s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_5</td>
                <td><small>project = PLAT AND updated >= '2024-01-06'...</small></td>
                <td>1,250</td>
                <td>1,225</td>
                <td class="status-red">2.04%</td>
                <td>40.00s</td>
                <td>1.50s</td>
                <td>37.00s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_6</td>
                <td><small>project = PLAT AND updated >= '2024-01-07'...</small></td>
                <td>1,300</td>
                <td>1,270</td>
                <td class="status-red">2.36%</td>
                <td>42.00s</td>
                <td>1.60s</td>
                <td>38.80s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_7</td>
                <td><small>project = PLAT AND updated >= '2024-01-08'...</small></td>
                <td>1,350</td>
                <td>1,315</td>
                <td class="status-red">2.66%</td>
                <td>44.00s</td>
                <td>1.70s</td>
                <td>40.60s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_8</td>
                <td><small>project = PLAT AND updated >= '2024-01-09'...</small></td>
                <td>1,400</td>
                <td>1,360</td>
                <td class="status-red">2.94%</td>
                <td>46.00s</td>
                <td>1.80s</td>
                <td>42.40s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_9</td>
                <td><small>project = PLAT AND updated >= '2024-01-10'...</small></td>
                <td>1,450</td>
                <td>1,405</td>
                <td class="status-red">3.20%</td>
                <td>48.00s</td>
                <td>1.90s</td>
                <td>44.20s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_10</td>
                <td><small>project = PLAT AND updated >= '2024-01-11'...</small></td>
                <td>1,500</td>
                <td>1,450</td>
                <td class="status-red">3.45%</td>
                <td>50.00s</td>
                <td>2.00s</td>
                <td>46.00s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_11</td>
                <td><small>project = PLAT AND updated >= '2024-01-12'...</small></td>
                <td>1,550</td>
                <td>1,495</td>
                <td class="status-red">3.68%</td>
                <td>52.00s</td>
                <td>2.10s</td>
                <td>47.80s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_12</td>
                <td><small>project = PLAT AND updated >= '2024-01-13'...</small></td>
                <td>1,600</td>
                <td>1,540</td>
                <td class="status-red">3.90%</td>
                <td>54.00s</td>
                <td>2.20s</td>
                <td>49.60s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_13</td>
                <td><small>project = PLAT AND updated >= '2024-01-14'...</small></td>
                <td>1,650</td>
                <td>1,585</td>
                <td class="status-red">4.10%</td>
                <td>56.00s</td>
                <td>2.30s</td>
                <td>51.40s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_14</td>
                <td><small>project = PLAT AND updated >= '2024-01-15'...</small></td>
                <td>1,700</td>
                <td>1,630</td>
                <td class="status-red">4.29%</td>
                <td>58.00s</td>
                <td>2.40s</td>
                <td>53.20s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_15</td>
                <td><small>project = PLAT AND updated >= '2024-01-16'...</small></td>
                <td>1,750</td>
                <td>1,675</td>
                <td class="status-red">4.48%</td>
                <td>60.00s</td>
                <td>2.50s</td>
                <td>55.00s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_16</td>
                <td><small>project = PLAT AND updated >= '2024-01-17'...</small></td>
                <td>1,800</td>
                <td>1,720</td>
                <td class="status-red">4.65%</td>
                <td>62.00s</td>
                <td>2.60s</td>
                <td>56.80s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_17</td>
                <td><small>project = PLAT AND updated >= '2024-01-18'...</small></td>
                <td>1,850</td>
                <td>1,765</td>
                <td class="status-red">4.82%</td>
                <td>64.00s</td>
                <td>2.70s</td>
                <td>58.60s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_18</td>
                <td><small>project = PLAT AND updated >= '2024-01-19'...</small></td>
                <td>1,900</td>
                <td>1,810</td>
                <td class="status-red">4.97%</td>
                <td>66.00s</td>
                <td>2.80s</td>
                <td>60.40s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_19</td>
                <td><small>project = PLAT AND updated >= '2024-01-20'...</small></td>
                <td>1,950</td>
                <td>1,855</td>
                <td class="status-red">5.12%</td>
                <td>68.00s</td>
                <td>2.90s</td>
                <td>62.20s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_20</td>
                <td><small>project = PLAT AND updated >= '2024-01-21'...</small></td>
                <td>2,000</td>
                <td>1,900</td>
                <td class="status-red">5.26%</td>
                <td>70.00s</td>
                <td>3.00s</td>
                <td>64.00s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_21</td>
                <td><small>project = PLAT AND updated >= '2024-01-22'...</small></td>
                <td>2,050</td>
                <td>1,945</td>
                <td class="status-red">5.40%</td>
                <td>72.00s</td>
                <td>3.10s</td>
                <td>65.80s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_22</td>
                <td><small>project = PLAT AND updated >= '2024-01-23'...</small></td>
                <td>2,100</td>
                <td>1,990</td>
                <td class="status-red">5.53%</td>
                <td>74.00s</td>
                <td>3.20s</td>
                <td>67.60s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_23</td>
                <td><small>project = PLAT AND updated >= '2024-01-24'...</small></td>
                <td>2,150</td>
                <td>2,035</td>
                <td class="status-red">5.65%</td>
                <td>76.00s</td>
                <td>3.30s</td>
                <td>69.40s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_24</td>
                <td><small>project = PLAT AND updated >= '2024-01-25'...</small></td>
                <td>2,200</td>
                <td>2,080</td>
                <td class="status-red">5.77%</td>
                <td>78.00s</td>
                <td>3.40s</td>
                <td>71.20s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_25</td>
                <td><small>project = PLAT AND updated >= '2024-01-26'...</small></td>
                <td>2,250</td>
                <td>2,125</td>
                <td class="status-red">5.88%</td>
                <td>80.00s</td>
                <td>3.50s</td>
                <td>73.00s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_26</td>
                <td><small>project = PLAT AND updated >= '2024-01-27'...</small></td>
                <td>2,300</td>
                <td>2,170</td>
                <td class="status-red">5.99%</td>
                <td>82.00s</td>
                <td>3.60s</td>
                <td>74.80s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_27</td>
                <td><small>project = PLAT AND updated >= '2024-01-28'...</small></td>
                <td>2,350</td>
                <td>2,215</td>
                <td class="status-red">6.09%</td>
                <td>84.00s</td>
                <td>3.70s</td>
                <td>76.60s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_28</td>
                <td><small>project = PLAT AND updated >= '2024-01-29'...</small></td>
                <td>2,400</td>
                <td>2,260</td>
                <td class="status-red">6.19%</td>
                <td>86.00s</td>
                <td>3.80s</td>
                <td>78.40s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_29</td>
                <td><small>project = PLAT AND updated >= '2024-01-30'...</small></td>
                <td>2,450</td>
                <td>2,305</td>
                <td class="status-red">6.29%</td>
                <td>88.00s</td>
                <td>3.90s</td>
                <td>80.20s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_30</td>
                <td><small>project = PLAT AND updated >= '2024-01-31'...</small></td>
                <td>2,500</td>
                <td>2,350</td>
                <td class="status-red">6.38%</td>
                <td>90.00s</td>
                <td>4.00s</td>
                <td>82.00s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_31</td>
                <td><small>project = PLAT AND updated >= '2024-01-32'...</small></td>
                <td>2,550</td>
                <td>2,395</td>
                <td class="status-red">6.47%</td>
                <td>92.00s</td>
                <td>4.10s</td>
                <td>83.80s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_32</td>
                <td><small>project = PLAT AND updated >= '2024-01-33'...</small></td>
                <td>2,600</td>
                <td>2,440</td>
                <td class="status-red">6.56%</td>
                <td>94.00s</td>
                <td>4.20s</td>
                <td>85.60s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_33</td>
                <td><small>project = PLAT AND updated >= '2024-01-34'...</small></td>
                <td>2,650</td>
                <td>2,485</td>
                <td class="status-red">6.64%</td>
                <td>96.00s</td>
                <td>4.30s</td>
                <td>87.40s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_34</td>
                <td><small>project = PLAT AND updated >= '2024-01-35'...</small></td>
                <td>2,700</td>
                <td>2,530</td>
                <td class="status-red">6.72%</td>
                <td>98.00s</td>
                <td>4.40s</td>
                <td>89.20s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_35</td>
                <td><small>project = PLAT AND updated >= '2024-01-36'...</small></td>
                <td>2,750</td>
                <td>2,575</td>
                <td class="status-red">6.80%</td>
                <td>100.00s</td>
                <td>4.50s</td>
                <td>91.00s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_36</td>
                <td><small>project = PLAT AND updated >= '2024-01-37'...</small></td>
                <td>2,800</td>
                <td>2,620</td>
                <td class="status-red">6.87%</td>
                <td>102.00s</td>
                <td>4.60s</td>
                <td>92.80s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_37</td>
                <td><small>project = PLAT AND updated >= '2024-01-38'...</small></td>
                <td>2,850</td>
                <td>2,665</td>
                <td class="status-red">6.94%</td>
                <td>104.00s</td>
                <td>4.70s</td>
                <td>94.60s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_38</td>
                <td><small>project = PLAT AND updated >= '2024-01-39'...</small></td>
                <td>2,900</td>
                <td>2,710</td>
                <td class="status-red">7.01%</td>
                <td>106.00s</td>
                <td>4.80s</td>
                <td>96.40s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_39</td>
                <td><small>project = PLAT AND updated >= '2024-01-40'...</small></td>
                <td>2,950</td>
                <td>2,755</td>
                <td class="status-red">7.08%</td>
                <td>108.00s</td>
                <td>4.90s</td>
                <td>98.20s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_40</td>
                <td><small>project = PLAT AND updated >= '2024-01-41'...</small></td>
                <td>3,000</td>
                <td>2,800</td>
                <td class="status-red">7.14%</td>
                <td>110.00s</td>
                <td>5.00s</td>
                <td>100.00s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_41</td>
                <td><small>project = PLAT AND updated >= '2024-01-42'...</small></td>
                <td>3,050</td>
                <td>2,845</td>
                <td class="status-red">7.21%</td>
                <td>112.00s</td>
                <td>5.10s</td>
                <td>101.80s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_42</td>
                <td><small>project = PLAT AND updated >= '2024-01-43'...</small></td>
                <td>3,100</td>
                <td>2,890</td>
                <td class="status-red">7.27%</td>
                <td>114.00s</td>
                <td>5.20s</td>
                <td>103.60s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_43</td>
                <td><small>project = PLAT AND updated >= '2024-01-44'...</small></td>
                <td>3,150</td>
                <td>2,935</td>
                <td class="status-red">7.33%</td>
                <td>116.00s</td>
                <td>5.30s</td>
                <td>105.40s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_44</td>
                <td><small>project = PLAT AND updated >= '2024-01-45'...</small></td>
                <td>3,200</td>
                <td>2,980</td>
                <td class="status-red">7.38%</td>
                <td>118.00s</td>
                <td>5.40s</td>
                <td>107.20s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_45</td>
                <td><small>project = PLAT AND updated >= '2024-01-46'...</small></td>
                <td>3,250</td>
                <td>3,025</td>
                <td class="status-red">7.44%</td>
                <td>120.00s</td>
                <td>5.50s</td>
                <td>109.00s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_46</td>
                <td><small>project = PLAT AND updated >= '2024-01-47'...</small></td>
                <td>3,300</td>
                <td>3,070</td>
                <td class="status-red">7.49%</td>
                <td>122.00s</td>
                <td>5.60s</td>
                <td>110.80s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_47</td>
                <td><small>project = PLAT AND updated >= '2024-01-48'...</small></td>
                <td>3,350</td>
                <td>3,115</td>
                <td class="status-red">7.54%</td>
                <td>124.00s</td>
                <td>5.70s</td>
                <td>112.60s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_48</td>
                <td><small>project = PLAT AND updated >= '2024-01-49'...</small></td>
                <td>3,400</td>
                <td>3,160</td>
                <td class="status-red">7.59%</td>
                <td>126.00s</td>
                <td>5.80s</td>
                <td>114.40s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>producer_49</td>
                <td><small>project = PLAT AND updated >= '2024-01-50'...</small></td>
                <td>3,450</td>
                <td>3,205</td>
                <td class="status-red">7.64%</td>
                <td>128.00s</td>
                <td>5.90s</td>
                <td>116.20s</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Producer Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div id="producerTimelineChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
        
            <div class="tab-pane fade" id="consumers" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Consumer Performance Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Consumer</th>
                                                <th>Type</th>
                                                <th>Messages Received</th>
                                                <th>Messages Sent</th>
                                                <th>Processing Time</th>
                                                <th>Total Duration</th>
                                                <th>Scaling Events</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
            <tr>
                <td>consumer_0</td>
                <td>consume_type_0</td>
                <td>500</td>
                <td>500</td>
                <td>20.00s</td>
                <td>20.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_1</td>
                <td>consume_type_1</td>
                <td>600</td>
                <td>600</td>
                <td>23.00s</td>
                <td>23.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_2</td>
                <td>consume_type_2</td>
                <td>700</td>
                <td>700</td>
                <td>26.00s</td>
                <td>26.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_3</td>
                <td>consume_type_3</td>
                <td>800</td>
                <td>800</td>
                <td>29.00s</td>
                <td>29.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_4</td>
                <td>consume_type_4</td>
                <td>900</td>
                <td>900</td>
                <td>32.00s</td>
                <td>32.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_5</td>
                <td>consume_type_0</td>
                <td>1,000</td>
                <td>1,000</td>
                <td>35.00s</td>
                <td>35.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_6</td>
                <td>consume_type_1</td>
                <td>1,100</td>
                <td>1,100</td>
                <td>38.00s</td>
                <td>38.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_7</td>
                <td>consume_type_2</td>
                <td>1,200</td>
                <td>1,200</td>
                <td>41.00s</td>
                <td>41.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_8</td>
                <td>consume_type_3</td>
                <td>1,300</td>
                <td>1,300</td>
                <td>44.00s</td>
                <td>44.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_9</td>
                <td>consume_type_4</td>
                <td>1,400</td>
                <td>1,400</td>
                <td>47.00s</td>
                <td>47.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_10</td>
                <td>consume_type_0</td>
                <td>1,500</td>
                <td>1,500</td>
                <td>50.00s</td>
                <td>50.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_11</td>
                <td>consume_type_1</td>
                <td>1,600</td>
                <td>1,600</td>
                <td>53.00s</td>
                <td>53.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_12</td>
                <td>consume_type_2</td>
                <td>1,700</td>
                <td>1,700</td>
                <td>56.00s</td>
                <td>56.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_13</td>
                <td>consume_type_3</td>
                <td>1,800</td>
                <td>1,800</td>
                <td>59.00s</td>
                <td>59.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_14</td>
                <td>consume_type_4</td>
                <td>1,900</td>
                <td>1,900</td>
                <td>62.00s</td>
                <td>62.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_15</td>
                <td>consume_type_0</td>
                <td>2,000</td>
                <td>2,000</td>
                <td>65.00s</td>
                <td>65.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_16</td>
                <td>consume_type_1</td>
                <td>2,100</td>
                <td>2,100</td>
                <td>68.00s</td>
                <td>68.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_17</td>
                <td>consume_type_2</td>
                <td>2,200</td>
                <td>2,200</td>
                <td>71.00s</td>
                <td>71.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_18</td>
                <td>consume_type_3</td>
                <td>2,300</td>
                <td>2,300</td>
                <td>74.00s</td>
                <td>74.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
            <tr>
                <td>consumer_19</td>
                <td>consume_type_4</td>
                <td>2,400</td>
                <td>2,400</td>
                <td>77.00s</td>
                <td>77.00s</td>
                <td>0</td>
                <td><span class="badge bg-success">completed</span></td>
            </tr>
            
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Consumer Throughput</h5>
                            </div>
                            <div class="card-body">
                                <div id="consumerThroughputChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Scaling Events Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div id="scalingEventsChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
        
            <div class="tab-pane fade" id="api" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>API Call Statistics</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Endpoint</th>
                                                <th>Total Requests</th>
                                                <th>Success (No Retry)</th>
                                                <th>Success (With Retry)</th>
                                                <th>Failed</th>
                                                <th>Success Rate</th>
                                                <th>Retry Rate</th>
                                                <th>Avg Time (Excl. Wait)</th>
                                                <th>Avg Time (Incl. Wait)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Retry Distribution</h5>
                            </div>
                            <div class="card-body">
                                <div id="retryDistributionChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Error Types</h5>
                            </div>
                            <div class="card-body">
                                <div id="errorTypesChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
        
            <div class="tab-pane fade" id="operations" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>System Resource Usage</h5>
                            </div>
                            <div class="card-body">
                                <div id="systemResourceChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Processing Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div id="processingTimelineChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Detailed Logs and Events</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Timestamp</th>
                                                <th>Component</th>
                                                <th>Event</th>
                                                <th>Details</th>
                                            </tr>
                                        </thead>
                                        <tbody id="operationalLogTable">
                                            <!-- Populated by JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    
    <script>
        
        // Chart data
        const producerData = {"names": ["producer_0", "producer_1", "producer_2", "producer_3", "producer_4", "producer_5", "producer_6", "producer_7", "producer_8", "producer_9", "producer_10", "producer_11", "producer_12", "producer_13", "producer_14", "producer_15", "producer_16", "producer_17", "producer_18", "producer_19", "producer_20", "producer_21", "producer_22", "producer_23", "producer_24", "producer_25", "producer_26", "producer_27", "producer_28", "producer_29", "producer_30", "producer_31", "producer_32", "producer_33", "producer_34", "producer_35", "producer_36", "producer_37", "producer_38", "producer_39", "producer_40", "producer_41", "producer_42", "producer_43", "producer_44", "producer_45", "producer_46", "producer_47", "producer_48", "producer_49"], "records": [1000, 1050, 1100, 1150, 1200, 1250, 1300, 1350, 1400, 1450, 1500, 1550, 1600, 1650, 1700, 1750, 1800, 1850, 1900, 1950, 2000, 2050, 2100, 2150, 2200, 2250, 2300, 2350, 2400, 2450, 2500, 2550, 2600, 2650, 2700, 2750, 2800, 2850, 2900, 2950, 3000, 3050, 3100, 3150, 3200, 3250, 3300, 3350, 3400, 3450], "timeline": [{"x": ["2025-08-28T11:31:10.066983", "2025-08-28T11:31:40.066983"], "y": ["producer_0", "producer_0"], "mode": "lines", "line": {"width": 10}, "name": "producer_0", "showlegend": false}, {"x": ["2025-08-28T11:31:20.066983", "2025-08-28T11:31:52.066983"], "y": ["producer_1", "producer_1"], "mode": "lines", "line": {"width": 10}, "name": "producer_1", "showlegend": false}, {"x": ["2025-08-28T11:31:30.066983", "2025-08-28T11:32:04.066983"], "y": ["producer_2", "producer_2"], "mode": "lines", "line": {"width": 10}, "name": "producer_2", "showlegend": false}, {"x": ["2025-08-28T11:31:40.066983", "2025-08-28T11:32:16.066983"], "y": ["producer_3", "producer_3"], "mode": "lines", "line": {"width": 10}, "name": "producer_3", "showlegend": false}, {"x": ["2025-08-28T11:31:50.066983", "2025-08-28T11:32:28.066983"], "y": ["producer_4", "producer_4"], "mode": "lines", "line": {"width": 10}, "name": "producer_4", "showlegend": false}, {"x": ["2025-08-28T11:32:00.066983", "2025-08-28T11:32:40.066983"], "y": ["producer_5", "producer_5"], "mode": "lines", "line": {"width": 10}, "name": "producer_5", "showlegend": false}, {"x": ["2025-08-28T11:32:10.066983", "2025-08-28T11:32:52.066983"], "y": ["producer_6", "producer_6"], "mode": "lines", "line": {"width": 10}, "name": "producer_6", "showlegend": false}, {"x": ["2025-08-28T11:32:20.066983", "2025-08-28T11:33:04.066983"], "y": ["producer_7", "producer_7"], "mode": "lines", "line": {"width": 10}, "name": "producer_7", "showlegend": false}, {"x": ["2025-08-28T11:32:30.066983", "2025-08-28T11:33:16.066983"], "y": ["producer_8", "producer_8"], "mode": "lines", "line": {"width": 10}, "name": "producer_8", "showlegend": false}, {"x": ["2025-08-28T11:32:40.066983", "2025-08-28T11:33:28.066983"], "y": ["producer_9", "producer_9"], "mode": "lines", "line": {"width": 10}, "name": "producer_9", "showlegend": false}, {"x": ["2025-08-28T11:32:50.066983", "2025-08-28T11:33:40.066983"], "y": ["producer_10", "producer_10"], "mode": "lines", "line": {"width": 10}, "name": "producer_10", "showlegend": false}, {"x": ["2025-08-28T11:33:00.066983", "2025-08-28T11:33:52.066983"], "y": ["producer_11", "producer_11"], "mode": "lines", "line": {"width": 10}, "name": "producer_11", "showlegend": false}, {"x": ["2025-08-28T11:33:10.066983", "2025-08-28T11:34:04.066983"], "y": ["producer_12", "producer_12"], "mode": "lines", "line": {"width": 10}, "name": "producer_12", "showlegend": false}, {"x": ["2025-08-28T11:33:20.066983", "2025-08-28T11:34:16.066983"], "y": ["producer_13", "producer_13"], "mode": "lines", "line": {"width": 10}, "name": "producer_13", "showlegend": false}, {"x": ["2025-08-28T11:33:30.066983", "2025-08-28T11:34:28.066983"], "y": ["producer_14", "producer_14"], "mode": "lines", "line": {"width": 10}, "name": "producer_14", "showlegend": false}, {"x": ["2025-08-28T11:33:40.066983", "2025-08-28T11:34:40.066983"], "y": ["producer_15", "producer_15"], "mode": "lines", "line": {"width": 10}, "name": "producer_15", "showlegend": false}, {"x": ["2025-08-28T11:33:50.066983", "2025-08-28T11:34:52.066983"], "y": ["producer_16", "producer_16"], "mode": "lines", "line": {"width": 10}, "name": "producer_16", "showlegend": false}, {"x": ["2025-08-28T11:34:00.066983", "2025-08-28T11:35:04.066983"], "y": ["producer_17", "producer_17"], "mode": "lines", "line": {"width": 10}, "name": "producer_17", "showlegend": false}, {"x": ["2025-08-28T11:34:10.066983", "2025-08-28T11:35:16.066983"], "y": ["producer_18", "producer_18"], "mode": "lines", "line": {"width": 10}, "name": "producer_18", "showlegend": false}, {"x": ["2025-08-28T11:34:20.066983", "2025-08-28T11:35:28.066983"], "y": ["producer_19", "producer_19"], "mode": "lines", "line": {"width": 10}, "name": "producer_19", "showlegend": false}, {"x": ["2025-08-28T11:34:30.066983", "2025-08-28T11:35:40.066983"], "y": ["producer_20", "producer_20"], "mode": "lines", "line": {"width": 10}, "name": "producer_20", "showlegend": false}, {"x": ["2025-08-28T11:34:40.066983", "2025-08-28T11:35:52.066983"], "y": ["producer_21", "producer_21"], "mode": "lines", "line": {"width": 10}, "name": "producer_21", "showlegend": false}, {"x": ["2025-08-28T11:34:50.066983", "2025-08-28T11:36:04.066983"], "y": ["producer_22", "producer_22"], "mode": "lines", "line": {"width": 10}, "name": "producer_22", "showlegend": false}, {"x": ["2025-08-28T11:35:00.066983", "2025-08-28T11:36:16.066983"], "y": ["producer_23", "producer_23"], "mode": "lines", "line": {"width": 10}, "name": "producer_23", "showlegend": false}, {"x": ["2025-08-28T11:35:10.066983", "2025-08-28T11:36:28.066983"], "y": ["producer_24", "producer_24"], "mode": "lines", "line": {"width": 10}, "name": "producer_24", "showlegend": false}, {"x": ["2025-08-28T11:35:20.066983", "2025-08-28T11:36:40.066983"], "y": ["producer_25", "producer_25"], "mode": "lines", "line": {"width": 10}, "name": "producer_25", "showlegend": false}, {"x": ["2025-08-28T11:35:30.066983", "2025-08-28T11:36:52.066983"], "y": ["producer_26", "producer_26"], "mode": "lines", "line": {"width": 10}, "name": "producer_26", "showlegend": false}, {"x": ["2025-08-28T11:35:40.066983", "2025-08-28T11:37:04.066983"], "y": ["producer_27", "producer_27"], "mode": "lines", "line": {"width": 10}, "name": "producer_27", "showlegend": false}, {"x": ["2025-08-28T11:35:50.066983", "2025-08-28T11:37:16.066983"], "y": ["producer_28", "producer_28"], "mode": "lines", "line": {"width": 10}, "name": "producer_28", "showlegend": false}, {"x": ["2025-08-28T11:36:00.066983", "2025-08-28T11:37:28.066983"], "y": ["producer_29", "producer_29"], "mode": "lines", "line": {"width": 10}, "name": "producer_29", "showlegend": false}, {"x": ["2025-08-28T11:36:10.066983", "2025-08-28T11:37:40.066983"], "y": ["producer_30", "producer_30"], "mode": "lines", "line": {"width": 10}, "name": "producer_30", "showlegend": false}, {"x": ["2025-08-28T11:36:20.066983", "2025-08-28T11:37:52.066983"], "y": ["producer_31", "producer_31"], "mode": "lines", "line": {"width": 10}, "name": "producer_31", "showlegend": false}, {"x": ["2025-08-28T11:36:30.066983", "2025-08-28T11:38:04.066983"], "y": ["producer_32", "producer_32"], "mode": "lines", "line": {"width": 10}, "name": "producer_32", "showlegend": false}, {"x": ["2025-08-28T11:36:40.066983", "2025-08-28T11:38:16.066983"], "y": ["producer_33", "producer_33"], "mode": "lines", "line": {"width": 10}, "name": "producer_33", "showlegend": false}, {"x": ["2025-08-28T11:36:50.066983", "2025-08-28T11:38:28.066983"], "y": ["producer_34", "producer_34"], "mode": "lines", "line": {"width": 10}, "name": "producer_34", "showlegend": false}, {"x": ["2025-08-28T11:37:00.066983", "2025-08-28T11:38:40.066983"], "y": ["producer_35", "producer_35"], "mode": "lines", "line": {"width": 10}, "name": "producer_35", "showlegend": false}, {"x": ["2025-08-28T11:37:10.066983", "2025-08-28T11:38:52.066983"], "y": ["producer_36", "producer_36"], "mode": "lines", "line": {"width": 10}, "name": "producer_36", "showlegend": false}, {"x": ["2025-08-28T11:37:20.066983", "2025-08-28T11:39:04.066983"], "y": ["producer_37", "producer_37"], "mode": "lines", "line": {"width": 10}, "name": "producer_37", "showlegend": false}, {"x": ["2025-08-28T11:37:30.066983", "2025-08-28T11:39:16.066983"], "y": ["producer_38", "producer_38"], "mode": "lines", "line": {"width": 10}, "name": "producer_38", "showlegend": false}, {"x": ["2025-08-28T11:37:40.066983", "2025-08-28T11:39:28.066983"], "y": ["producer_39", "producer_39"], "mode": "lines", "line": {"width": 10}, "name": "producer_39", "showlegend": false}, {"x": ["2025-08-28T11:37:50.066983", "2025-08-28T11:39:40.066983"], "y": ["producer_40", "producer_40"], "mode": "lines", "line": {"width": 10}, "name": "producer_40", "showlegend": false}, {"x": ["2025-08-28T11:38:00.066983", "2025-08-28T11:39:52.066983"], "y": ["producer_41", "producer_41"], "mode": "lines", "line": {"width": 10}, "name": "producer_41", "showlegend": false}, {"x": ["2025-08-28T11:38:10.066983", "2025-08-28T11:40:04.066983"], "y": ["producer_42", "producer_42"], "mode": "lines", "line": {"width": 10}, "name": "producer_42", "showlegend": false}, {"x": ["2025-08-28T11:38:20.066983", "2025-08-28T11:40:16.066983"], "y": ["producer_43", "producer_43"], "mode": "lines", "line": {"width": 10}, "name": "producer_43", "showlegend": false}, {"x": ["2025-08-28T11:38:30.066983", "2025-08-28T11:40:28.066983"], "y": ["producer_44", "producer_44"], "mode": "lines", "line": {"width": 10}, "name": "producer_44", "showlegend": false}, {"x": ["2025-08-28T11:38:40.066983", "2025-08-28T11:40:40.066983"], "y": ["producer_45", "producer_45"], "mode": "lines", "line": {"width": 10}, "name": "producer_45", "showlegend": false}, {"x": ["2025-08-28T11:38:50.066983", "2025-08-28T11:40:52.066983"], "y": ["producer_46", "producer_46"], "mode": "lines", "line": {"width": 10}, "name": "producer_46", "showlegend": false}, {"x": ["2025-08-28T11:39:00.066983", "2025-08-28T11:41:04.066983"], "y": ["producer_47", "producer_47"], "mode": "lines", "line": {"width": 10}, "name": "producer_47", "showlegend": false}, {"x": ["2025-08-28T11:39:10.066983", "2025-08-28T11:41:16.066983"], "y": ["producer_48", "producer_48"], "mode": "lines", "line": {"width": 10}, "name": "producer_48", "showlegend": false}, {"x": ["2025-08-28T11:39:20.066983", "2025-08-28T11:41:28.066983"], "y": ["producer_49", "producer_49"], "mode": "lines", "line": {"width": 10}, "name": "producer_49", "showlegend": false}]};
        const consumerData = {"names": ["consumer_0", "consumer_1", "consumer_2", "consumer_3", "consumer_4", "consumer_5", "consumer_6", "consumer_7", "consumer_8", "consumer_9", "consumer_10", "consumer_11", "consumer_12", "consumer_13", "consumer_14", "consumer_15", "consumer_16", "consumer_17", "consumer_18", "consumer_19"], "throughput": [25.0, 26.08695652173913, 26.923076923076923, 27.586206896551722, 28.125, 28.571428571428573, 28.94736842105263, 29.26829268292683, 29.545454545454547, 29.78723404255319, 30.0, 30.18867924528302, 30.357142857142858, 30.508474576271187, 30.64516129032258, 30.76923076923077, 30.88235294117647, 30.985915492957748, 31.08108108108108, 31.16883116883117]};
        const apiData = {"endpoints": [], "requests": [], "retryLabels": [], "retryCounts": [], "errorTypes": [], "errorCounts": []};

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
        });

        function initializeCharts() {
            // Producer Overview Chart
            const producerOverview = {
                data: [{
                    x: producerData.names,
                    y: producerData.records,
                    type: 'bar',
                    name: 'Records Processed',
                    marker: { color: '#007bff' }
                }],
                layout: {
                    title: 'Records Processed by Producer',
                    xaxis: { title: 'Producer' },
                    yaxis: { title: 'Records' },
                    responsive: true
                }
            };
            Plotly.newPlot('producerOverviewChart', producerOverview.data, producerOverview.layout);

            // API Distribution Chart
            const apiDistribution = {
                data: [{
                    labels: apiData.endpoints,
                    values: apiData.requests,
                    type: 'pie',
                    textinfo: 'label+percent',
                    textposition: 'outside'
                }],
                layout: {
                    title: 'API Requests Distribution',
                    responsive: true
                }
            };
            Plotly.newPlot('apiDistributionChart', apiDistribution.data, apiDistribution.layout);

            // Producer Timeline Chart
            const producerTimeline = {
                data: producerData.timeline,
                layout: {
                    title: 'Producer Processing Timeline',
                    xaxis: { title: 'Time' },
                    yaxis: { title: 'Producer' },
                    responsive: true
                }
            };
            Plotly.newPlot('producerTimelineChart', producerTimeline.data, producerTimeline.layout);

            // Consumer Throughput Chart
            const consumerThroughput = {
                data: [{
                    x: consumerData.names,
                    y: consumerData.throughput,
                    type: 'bar',
                    name: 'Messages/Second',
                    marker: { color: '#28a745' }
                }],
                layout: {
                    title: 'Consumer Throughput',
                    xaxis: { title: 'Consumer' },
                    yaxis: { title: 'Messages/Second' },
                    responsive: true
                }
            };
            Plotly.newPlot('consumerThroughputChart', consumerThroughput.data, consumerThroughput.layout);

            // Retry Distribution Chart
            const retryDistribution = {
                data: [{
                    x: apiData.retryLabels,
                    y: apiData.retryCounts,
                    type: 'bar',
                    name: 'Request Count',
                    marker: { color: '#ffc107' }
                }],
                layout: {
                    title: 'API Retry Distribution',
                    xaxis: { title: 'Retry Count' },
                    yaxis: { title: 'Requests' },
                    responsive: true
                }
            };
            Plotly.newPlot('retryDistributionChart', retryDistribution.data, retryDistribution.layout);

            // Error Types Chart
            if (apiData.errorTypes.length > 0) {
                const errorTypes = {
                    data: [{
                        labels: apiData.errorTypes,
                        values: apiData.errorCounts,
                        type: 'pie',
                        textinfo: 'label+percent'
                    }],
                    layout: {
                        title: 'API Error Types',
                        responsive: true
                    }
                };
                Plotly.newPlot('errorTypesChart', errorTypes.data, errorTypes.layout);
            }
        }
        
    </script>
</body>
</html>
        