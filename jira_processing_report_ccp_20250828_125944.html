
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JIRA Processing Report - ccp</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        
        .status-green { background-color: #d4edda; color: #155724; }
        .status-amber { background-color: #fff3cd; color: #856404; }
        .status-red { background-color: #f8d7da; color: #721c24; }
        .metric-card { border-left: 4px solid #007bff; }
        .chart-container { min-height: 400px; }
        .nav-pills .nav-link.active { background-color: #007bff; }
        .table-responsive { max-height: 500px; overflow-y: auto; }
        
    </style>
</head>
<body>
    <div class="container-fluid">
        
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h1 class="mb-0">JIRA Processing Report - ccp</h1>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Report Generated:</strong><br>
                                2025-08-28 12:59:44
                            </div>
                            <div class="col-md-3">
                                <strong>Processing Duration:</strong><br>
                                14.23 seconds
                            </div>
                            <div class="col-md-3">
                                <strong>Producers:</strong><br>
                                0
                            </div>
                            <div class="col-md-3">
                                <strong>Consumers:</strong><br>
                                0
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        
        <div class="row mb-4">
            <div class="col-12">
                <ul class="nav nav-pills" id="reportTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="executive-tab" data-bs-toggle="pill"
                                data-bs-target="#executive" type="button" role="tab">Executive Summary</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="producers-tab" data-bs-toggle="pill"
                                data-bs-target="#producers" type="button" role="tab">Producers</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="consumers-tab" data-bs-toggle="pill"
                                data-bs-target="#consumers" type="button" role="tab">Consumers</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="api-tab" data-bs-toggle="pill"
                                data-bs-target="#api" type="button" role="tab">API Metrics</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="operations-tab" data-bs-toggle="pill"
                                data-bs-target="#operations" type="button" role="tab">Operations</button>
                    </li>
                </ul>
            </div>
        </div>
        
        
        <div class="tab-content" id="reportTabsContent">
            <div class="tab-pane fade show active" id="executive" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">Total Records</h5>
                                <h2 class="text-primary">0</h2>
                                <small class="text-muted">Processed vs 0 estimated</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">Accuracy</h5>
                                <h2 class="status-green">0.00%</h2>
                                <small class="text-muted">Difference from estimate</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">API Success Rate</h5>
                                <h2 class="text-success">100.0%</h2>
                                <small class="text-muted">6 of 6 calls</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">Processing Time</h5>
                                <h2 class="text-info">0.0s</h2>
                                <small class="text-muted">Total producer time</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Producer Performance Overview</h5>
                            </div>
                            <div class="card-body">
                                <div id="producerOverviewChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>API Call Distribution</h5>
                            </div>
                            <div class="card-body">
                                <div id="apiDistributionChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
        
            <div class="tab-pane fade" id="producers" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Producer Performance Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Producer</th>
                                                <th>JQL Query</th>
                                                <th>Records Processed</th>
                                                <th>Records Estimate</th>
                                                <th>Accuracy</th>
                                                <th>Processing Time</th>
                                                <th>Waiting Time</th>
                                                <th>CPU Time</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Producer Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div id="producerTimelineChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
        
            <div class="tab-pane fade" id="consumers" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Consumer Performance Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Consumer</th>
                                                <th>Type</th>
                                                <th>Messages Received</th>
                                                <th>Messages Sent</th>
                                                <th>Processing Time</th>
                                                <th>Total Duration</th>
                                                <th>Scaling Events</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Consumer Throughput</h5>
                            </div>
                            <div class="card-body">
                                <div id="consumerThroughputChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Scaling Events Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div id="scalingEventsChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
        
            <div class="tab-pane fade" id="api" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>API Call Statistics</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Endpoint</th>
                                                <th>Total Requests</th>
                                                <th>Success (No Retry)</th>
                                                <th>Success (With Retry)</th>
                                                <th>Failed</th>
                                                <th>Success Rate</th>
                                                <th>Retry Rate</th>
                                                <th>Avg Time (Excl. Wait)</th>
                                                <th>Avg Time (Incl. Wait)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            
            <tr>
                <td><small>GET api/3/myself</small></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>100.0%</td>
                <td>0.0%</td>
                <td>0.579s</td>
                <td>0.579s</td>
            </tr>
            
            <tr>
                <td><small>POST api/3/search/approximate-count</small></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td>100.0%</td>
                <td>0.0%</td>
                <td>0.629s</td>
                <td>0.629s</td>
            </tr>
            
            <tr>
                <td><small>POST api/3/search/jql</small></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td>100.0%</td>
                <td>0.0%</td>
                <td>1.739s</td>
                <td>1.739s</td>
            </tr>
            
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Retry Distribution</h5>
                            </div>
                            <div class="card-body">
                                <div id="retryDistributionChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Error Types</h5>
                            </div>
                            <div class="card-body">
                                <div id="errorTypesChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        
        
            <div class="tab-pane fade" id="operations" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>System Resource Usage</h5>
                            </div>
                            <div class="card-body">
                                <div id="systemResourceChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Processing Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div id="processingTimelineChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Detailed Logs and Events</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Timestamp</th>
                                                <th>Component</th>
                                                <th>Event</th>
                                                <th>Details</th>
                                            </tr>
                                        </thead>
                                        <tbody id="operationalLogTable">
                                            <!-- Populated by JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
    </div>
    
    <script>
        
        // Chart data
        const producerData = {"names": [], "records": [], "timeline": []};
        const consumerData = {"names": [], "throughput": []};
        const apiData = {"endpoints": ["GET api/3/myself", "POST api/3/search/approximate-count", "POST api/3/search/jql"], "requests": [1, 1, 4], "retryLabels": [0], "retryCounts": [6], "errorTypes": [], "errorCounts": []};

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
        });

        function initializeCharts() {
            // Producer Overview Chart
            const producerOverview = {
                data: [{
                    x: producerData.names,
                    y: producerData.records,
                    type: 'bar',
                    name: 'Records Processed',
                    marker: { color: '#007bff' }
                }],
                layout: {
                    title: 'Records Processed by Producer',
                    xaxis: { title: 'Producer' },
                    yaxis: { title: 'Records' },
                    responsive: true
                }
            };
            Plotly.newPlot('producerOverviewChart', producerOverview.data, producerOverview.layout);

            // API Distribution Chart
            const apiDistribution = {
                data: [{
                    labels: apiData.endpoints,
                    values: apiData.requests,
                    type: 'pie',
                    textinfo: 'label+percent',
                    textposition: 'outside'
                }],
                layout: {
                    title: 'API Requests Distribution',
                    responsive: true
                }
            };
            Plotly.newPlot('apiDistributionChart', apiDistribution.data, apiDistribution.layout);

            // Producer Timeline Chart
            const producerTimeline = {
                data: producerData.timeline,
                layout: {
                    title: 'Producer Processing Timeline',
                    xaxis: { title: 'Time' },
                    yaxis: { title: 'Producer' },
                    responsive: true
                }
            };
            Plotly.newPlot('producerTimelineChart', producerTimeline.data, producerTimeline.layout);

            // Consumer Throughput Chart
            const consumerThroughput = {
                data: [{
                    x: consumerData.names,
                    y: consumerData.throughput,
                    type: 'bar',
                    name: 'Messages/Second',
                    marker: { color: '#28a745' }
                }],
                layout: {
                    title: 'Consumer Throughput',
                    xaxis: { title: 'Consumer' },
                    yaxis: { title: 'Messages/Second' },
                    responsive: true
                }
            };
            Plotly.newPlot('consumerThroughputChart', consumerThroughput.data, consumerThroughput.layout);

            // Retry Distribution Chart
            const retryDistribution = {
                data: [{
                    x: apiData.retryLabels,
                    y: apiData.retryCounts,
                    type: 'bar',
                    name: 'Request Count',
                    marker: { color: '#ffc107' }
                }],
                layout: {
                    title: 'API Retry Distribution',
                    xaxis: { title: 'Retry Count' },
                    yaxis: { title: 'Requests' },
                    responsive: true
                }
            };
            Plotly.newPlot('retryDistributionChart', retryDistribution.data, retryDistribution.layout);

            // Error Types Chart
            if (apiData.errorTypes.length > 0) {
                const errorTypes = {
                    data: [{
                        labels: apiData.errorTypes,
                        values: apiData.errorCounts,
                        type: 'pie',
                        textinfo: 'label+percent'
                    }],
                    layout: {
                        title: 'API Error Types',
                        responsive: true
                    }
                };
                Plotly.newPlot('errorTypesChart', errorTypes.data, errorTypes.layout);
            }
        }
        
    </script>
</body>
</html>
        