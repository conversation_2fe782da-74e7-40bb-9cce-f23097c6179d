"""
JIRA Processing Report Generator

Generates comprehensive HTML reports for producer and consumer activity
with executive KPIs and operational details.
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import asyncio
from logging import Logger


@dataclass
class ProducerMetrics:
    """Metrics for producer activities"""
    producer_name: str
    jql_query: str
    total_records_processed: int
    total_records_estimate: int
    difference_percentage: float
    processing_time: float
    waiting_time: float
    cpu_time: float
    start_time: datetime
    end_time: datetime
    status: str  # 'completed', 'failed', 'running'
    error_message: Optional[str] = None


@dataclass
class ConsumerMetrics:
    """Metrics for consumer activities"""
    consumer_name: str
    consumer_type: str  # 'consume_issues', 'consume_changelog', etc.
    messages_received: int
    messages_sent: int
    processing_time: float
    scaling_events: List[Dict[str, Any]]
    start_time: datetime
    end_time: Optional[datetime]
    status: str  # 'completed', 'failed', 'running'
    error_message: Optional[str] = None


@dataclass
class ApiCallMetrics:
    """Metrics for fetch_with_retries API calls"""
    endpoint: str
    total_requests: int
    successful_without_retries: int
    successful_with_retries: int
    failed_requests: int
    average_time_excluding_wait: float
    average_time_including_wait: float
    retry_distribution: Dict[int, int]  # retry_count -> frequency
    error_types: Dict[str, int]  # error_type -> count


class MetricsCollector:
    """Collects metrics during JIRA processing"""
    
    def __init__(self):
        self.producer_metrics: List[ProducerMetrics] = []
        self.consumer_metrics: List[ConsumerMetrics] = []
        self.api_metrics: Dict[str, ApiCallMetrics] = {}
        self.start_time = datetime.now()
        self.end_time: Optional[datetime] = None
        
    def add_producer_metrics(self, metrics: ProducerMetrics):
        """Add producer metrics"""
        self.producer_metrics.append(metrics)
        
    def add_consumer_metrics(self, metrics: ConsumerMetrics):
        """Add consumer metrics"""
        self.consumer_metrics.append(metrics)
        
    def update_api_metrics(self, endpoint: str, success: bool, retry_count: int, 
                          time_excluding_wait: float, time_including_wait: float,
                          error_type: Optional[str] = None):
        """Update API call metrics"""
        if endpoint not in self.api_metrics:
            self.api_metrics[endpoint] = ApiCallMetrics(
                endpoint=endpoint,
                total_requests=0,
                successful_without_retries=0,
                successful_with_retries=0,
                failed_requests=0,
                average_time_excluding_wait=0.0,
                average_time_including_wait=0.0,
                retry_distribution={},
                error_types={}
            )
        
        metrics = self.api_metrics[endpoint]
        metrics.total_requests += 1
        
        if success:
            if retry_count == 0:
                metrics.successful_without_retries += 1
            else:
                metrics.successful_with_retries += 1
        else:
            metrics.failed_requests += 1
            if error_type:
                metrics.error_types[error_type] = metrics.error_types.get(error_type, 0) + 1
        
        # Update retry distribution
        metrics.retry_distribution[retry_count] = metrics.retry_distribution.get(retry_count, 0) + 1
        
        # Update average times (simple running average)
        total_successful = metrics.successful_without_retries + metrics.successful_with_retries
        if total_successful > 0:
            metrics.average_time_excluding_wait = (
                (metrics.average_time_excluding_wait * (total_successful - 1) + time_excluding_wait) / total_successful
            )
            metrics.average_time_including_wait = (
                (metrics.average_time_including_wait * (total_successful - 1) + time_including_wait) / total_successful
            )
    
    def finalize(self):
        """Mark collection as complete"""
        self.end_time = datetime.now()


class JiraProcessingReportGenerator:
    """Generates comprehensive HTML reports for JIRA processing"""
    
    def __init__(self, metrics_collector: MetricsCollector, project_key: str):
        self.metrics = metrics_collector
        self.project_key = project_key
        self.report_timestamp = datetime.now()
        
    def generate_html_report(self, output_path: Optional[Path] = None) -> str:
        """Generate comprehensive HTML report"""
        if output_path is None:
            timestamp = self.report_timestamp.strftime("%Y%m%d_%H%M%S")
            output_path = Path(f"jira_processing_report_{self.project_key}_{timestamp}.html")
        
        html_content = self._generate_html_content()
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        return str(output_path)
    
    def _generate_html_content(self) -> str:
        """Generate the complete HTML content"""
        return f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JIRA Processing Report - {self.project_key}</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        {self._get_custom_css()}
    </style>
</head>
<body>
    <div class="container-fluid">
        {self._generate_header()}
        {self._generate_navigation()}
        {self._generate_executive_summary()}
        {self._generate_producer_details()}
        {self._generate_consumer_details()}
        {self._generate_api_metrics()}
        {self._generate_operational_details()}
    </div>
    
    <script>
        {self._generate_javascript()}
    </script>
</body>
</html>
        """
    
    def _get_custom_css(self) -> str:
        """Custom CSS for the report"""
        return """
        .status-green { background-color: #d4edda; color: #155724; }
        .status-amber { background-color: #fff3cd; color: #856404; }
        .status-red { background-color: #f8d7da; color: #721c24; }
        .metric-card { border-left: 4px solid #007bff; }
        .chart-container { min-height: 400px; }
        .nav-pills .nav-link.active { background-color: #007bff; }
        .table-responsive { max-height: 500px; overflow-y: auto; }
        """

    def _generate_header(self) -> str:
        """Generate report header"""
        total_duration = (self.metrics.end_time - self.metrics.start_time).total_seconds() if self.metrics.end_time else 0
        return f"""
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h1 class="mb-0">JIRA Processing Report - {self.project_key}</h1>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Report Generated:</strong><br>
                                {self.report_timestamp.strftime('%Y-%m-%d %H:%M:%S')}
                            </div>
                            <div class="col-md-3">
                                <strong>Processing Duration:</strong><br>
                                {total_duration:.2f} seconds
                            </div>
                            <div class="col-md-3">
                                <strong>Producers:</strong><br>
                                {len(self.metrics.producer_metrics)}
                            </div>
                            <div class="col-md-3">
                                <strong>Consumers:</strong><br>
                                {len(self.metrics.consumer_metrics)}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        """

    def _generate_navigation(self) -> str:
        """Generate navigation tabs"""
        return """
        <div class="row mb-4">
            <div class="col-12">
                <ul class="nav nav-pills" id="reportTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="executive-tab" data-bs-toggle="pill"
                                data-bs-target="#executive" type="button" role="tab">Executive Summary</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="producers-tab" data-bs-toggle="pill"
                                data-bs-target="#producers" type="button" role="tab">Producers</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="consumers-tab" data-bs-toggle="pill"
                                data-bs-target="#consumers" type="button" role="tab">Consumers</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="api-tab" data-bs-toggle="pill"
                                data-bs-target="#api" type="button" role="tab">API Metrics</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="operations-tab" data-bs-toggle="pill"
                                data-bs-target="#operations" type="button" role="tab">Operations</button>
                    </li>
                </ul>
            </div>
        </div>
        """

    def _generate_executive_summary(self) -> str:
        """Generate executive summary tab"""
        # Calculate overall metrics
        total_records = sum(p.total_records_processed for p in self.metrics.producer_metrics)
        total_estimate = sum(p.total_records_estimate for p in self.metrics.producer_metrics)
        overall_accuracy = abs(total_records - total_estimate) / total_estimate * 100 if total_estimate > 0 else 0

        # Status color coding
        accuracy_status = "status-green" if overall_accuracy < 0.1 else "status-amber" if overall_accuracy <= 1.0 else "status-red"

        # API success rate
        total_api_calls = sum(m.total_requests for m in self.metrics.api_metrics.values())
        successful_api_calls = sum(m.successful_without_retries + m.successful_with_retries for m in self.metrics.api_metrics.values())
        api_success_rate = (successful_api_calls / total_api_calls * 100) if total_api_calls > 0 else 0

        return f"""
        <div class="tab-content" id="reportTabsContent">
            <div class="tab-pane fade show active" id="executive" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">Total Records</h5>
                                <h2 class="text-primary">{total_records:,}</h2>
                                <small class="text-muted">Processed vs {total_estimate:,} estimated</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">Accuracy</h5>
                                <h2 class="{accuracy_status}">{overall_accuracy:.2f}%</h2>
                                <small class="text-muted">Difference from estimate</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">API Success Rate</h5>
                                <h2 class="text-success">{api_success_rate:.1f}%</h2>
                                <small class="text-muted">{successful_api_calls:,} of {total_api_calls:,} calls</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card metric-card">
                            <div class="card-body">
                                <h5 class="card-title">Processing Time</h5>
                                <h2 class="text-info">{sum(p.processing_time for p in self.metrics.producer_metrics):.1f}s</h2>
                                <small class="text-muted">Total producer time</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Producer Performance Overview</h5>
                            </div>
                            <div class="card-body">
                                <div id="producerOverviewChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>API Call Distribution</h5>
                            </div>
                            <div class="card-body">
                                <div id="apiDistributionChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        """

    def _generate_producer_details(self) -> str:
        """Generate producer details tab"""
        producer_rows = ""
        for producer in self.metrics.producer_metrics:
            accuracy = abs(producer.total_records_processed - producer.total_records_estimate) / producer.total_records_estimate * 100 if producer.total_records_estimate > 0 else 0
            status_class = "status-green" if accuracy < 0.1 else "status-amber" if accuracy <= 1.0 else "status-red"

            producer_rows += f"""
            <tr>
                <td>{producer.producer_name}</td>
                <td><small>{producer.jql_query[:50]}...</small></td>
                <td>{producer.total_records_processed:,}</td>
                <td>{producer.total_records_estimate:,}</td>
                <td class="{status_class}">{accuracy:.2f}%</td>
                <td>{producer.processing_time:.2f}s</td>
                <td>{producer.waiting_time:.2f}s</td>
                <td>{producer.cpu_time:.2f}s</td>
                <td><span class="badge bg-{'success' if producer.status == 'completed' else 'danger'}">{producer.status}</span></td>
            </tr>
            """

        return f"""
            <div class="tab-pane fade" id="producers" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Producer Performance Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Producer</th>
                                                <th>JQL Query</th>
                                                <th>Records Processed</th>
                                                <th>Records Estimate</th>
                                                <th>Accuracy</th>
                                                <th>Processing Time</th>
                                                <th>Waiting Time</th>
                                                <th>CPU Time</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {producer_rows}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Producer Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div id="producerTimelineChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        """

    def _generate_consumer_details(self) -> str:
        """Generate consumer details tab"""
        consumer_rows = ""
        for consumer in self.metrics.consumer_metrics:
            scaling_events_count = len(consumer.scaling_events)
            duration = (consumer.end_time - consumer.start_time).total_seconds() if consumer.end_time else 0

            consumer_rows += f"""
            <tr>
                <td>{consumer.consumer_name}</td>
                <td>{consumer.consumer_type}</td>
                <td>{consumer.messages_received:,}</td>
                <td>{consumer.messages_sent:,}</td>
                <td>{consumer.processing_time:.2f}s</td>
                <td>{duration:.2f}s</td>
                <td>{scaling_events_count}</td>
                <td><span class="badge bg-{'success' if consumer.status == 'completed' else 'warning' if consumer.status == 'running' else 'danger'}">{consumer.status}</span></td>
            </tr>
            """

        return f"""
            <div class="tab-pane fade" id="consumers" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Consumer Performance Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Consumer</th>
                                                <th>Type</th>
                                                <th>Messages Received</th>
                                                <th>Messages Sent</th>
                                                <th>Processing Time</th>
                                                <th>Total Duration</th>
                                                <th>Scaling Events</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {consumer_rows}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Consumer Throughput</h5>
                            </div>
                            <div class="card-body">
                                <div id="consumerThroughputChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Scaling Events Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div id="scalingEventsChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        """

    def _generate_api_metrics(self) -> str:
        """Generate API metrics tab"""
        api_rows = ""
        for endpoint, metrics in self.metrics.api_metrics.items():
            success_rate = ((metrics.successful_without_retries + metrics.successful_with_retries) / metrics.total_requests * 100) if metrics.total_requests > 0 else 0
            retry_rate = (metrics.successful_with_retries / metrics.total_requests * 100) if metrics.total_requests > 0 else 0

            api_rows += f"""
            <tr>
                <td><small>{endpoint}</small></td>
                <td>{metrics.total_requests:,}</td>
                <td>{metrics.successful_without_retries:,}</td>
                <td>{metrics.successful_with_retries:,}</td>
                <td>{metrics.failed_requests:,}</td>
                <td>{success_rate:.1f}%</td>
                <td>{retry_rate:.1f}%</td>
                <td>{metrics.average_time_excluding_wait:.3f}s</td>
                <td>{metrics.average_time_including_wait:.3f}s</td>
            </tr>
            """

        return f"""
            <div class="tab-pane fade" id="api" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>API Call Statistics</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>Endpoint</th>
                                                <th>Total Requests</th>
                                                <th>Success (No Retry)</th>
                                                <th>Success (With Retry)</th>
                                                <th>Failed</th>
                                                <th>Success Rate</th>
                                                <th>Retry Rate</th>
                                                <th>Avg Time (Excl. Wait)</th>
                                                <th>Avg Time (Incl. Wait)</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {api_rows}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Retry Distribution</h5>
                            </div>
                            <div class="card-body">
                                <div id="retryDistributionChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Error Types</h5>
                            </div>
                            <div class="card-body">
                                <div id="errorTypesChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        """

    def _generate_operational_details(self) -> str:
        """Generate operational details tab"""
        return """
            <div class="tab-pane fade" id="operations" role="tabpanel">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>System Resource Usage</h5>
                            </div>
                            <div class="card-body">
                                <div id="systemResourceChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Processing Timeline</h5>
                            </div>
                            <div class="card-body">
                                <div id="processingTimelineChart" class="chart-container"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>Detailed Logs and Events</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>Timestamp</th>
                                                <th>Component</th>
                                                <th>Event</th>
                                                <th>Details</th>
                                            </tr>
                                        </thead>
                                        <tbody id="operationalLogTable">
                                            <!-- Populated by JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        """

    def _generate_javascript(self) -> str:
        """Generate JavaScript for interactive charts"""
        # Prepare data for charts
        producer_data = self._prepare_producer_chart_data()
        consumer_data = self._prepare_consumer_chart_data()
        api_data = self._prepare_api_chart_data()

        return f"""
        // Chart data
        const producerData = {json.dumps(producer_data)};
        const consumerData = {json.dumps(consumer_data)};
        const apiData = {json.dumps(api_data)};

        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {{
            initializeCharts();
        }});

        function initializeCharts() {{
            // Producer Overview Chart
            const producerOverview = {{
                data: [{{
                    x: producerData.names,
                    y: producerData.records,
                    type: 'bar',
                    name: 'Records Processed',
                    marker: {{ color: '#007bff' }}
                }}],
                layout: {{
                    title: 'Records Processed by Producer',
                    xaxis: {{ title: 'Producer' }},
                    yaxis: {{ title: 'Records' }},
                    responsive: true
                }}
            }};
            Plotly.newPlot('producerOverviewChart', producerOverview.data, producerOverview.layout);

            // API Distribution Chart
            const apiDistribution = {{
                data: [{{
                    labels: apiData.endpoints,
                    values: apiData.requests,
                    type: 'pie',
                    textinfo: 'label+percent',
                    textposition: 'outside'
                }}],
                layout: {{
                    title: 'API Requests Distribution',
                    responsive: true
                }}
            }};
            Plotly.newPlot('apiDistributionChart', apiDistribution.data, apiDistribution.layout);

            // Producer Timeline Chart
            const producerTimeline = {{
                data: producerData.timeline,
                layout: {{
                    title: 'Producer Processing Timeline',
                    xaxis: {{ title: 'Time' }},
                    yaxis: {{ title: 'Producer' }},
                    responsive: true
                }}
            }};
            Plotly.newPlot('producerTimelineChart', producerTimeline.data, producerTimeline.layout);

            // Consumer Throughput Chart
            const consumerThroughput = {{
                data: [{{
                    x: consumerData.names,
                    y: consumerData.throughput,
                    type: 'bar',
                    name: 'Messages/Second',
                    marker: {{ color: '#28a745' }}
                }}],
                layout: {{
                    title: 'Consumer Throughput',
                    xaxis: {{ title: 'Consumer' }},
                    yaxis: {{ title: 'Messages/Second' }},
                    responsive: true
                }}
            }};
            Plotly.newPlot('consumerThroughputChart', consumerThroughput.data, consumerThroughput.layout);

            // Retry Distribution Chart
            const retryDistribution = {{
                data: [{{
                    x: apiData.retryLabels,
                    y: apiData.retryCounts,
                    type: 'bar',
                    name: 'Request Count',
                    marker: {{ color: '#ffc107' }}
                }}],
                layout: {{
                    title: 'API Retry Distribution',
                    xaxis: {{ title: 'Retry Count' }},
                    yaxis: {{ title: 'Requests' }},
                    responsive: true
                }}
            }};
            Plotly.newPlot('retryDistributionChart', retryDistribution.data, retryDistribution.layout);

            // Error Types Chart
            if (apiData.errorTypes.length > 0) {{
                const errorTypes = {{
                    data: [{{
                        labels: apiData.errorTypes,
                        values: apiData.errorCounts,
                        type: 'pie',
                        textinfo: 'label+percent'
                    }}],
                    layout: {{
                        title: 'API Error Types',
                        responsive: true
                    }}
                }};
                Plotly.newPlot('errorTypesChart', errorTypes.data, errorTypes.layout);
            }}
        }}
        """

    def _prepare_producer_chart_data(self) -> Dict[str, Any]:
        """Prepare data for producer charts"""
        names = [p.producer_name for p in self.metrics.producer_metrics]
        records = [p.total_records_processed for p in self.metrics.producer_metrics]

        # Timeline data for Gantt-like chart
        timeline_data = []
        for i, producer in enumerate(self.metrics.producer_metrics):
            timeline_data.append({
                'x': [producer.start_time.isoformat(), producer.end_time.isoformat()],
                'y': [producer.producer_name, producer.producer_name],
                'mode': 'lines',
                'line': {'width': 10},
                'name': producer.producer_name,
                'showlegend': False
            })

        return {
            'names': names,
            'records': records,
            'timeline': timeline_data
        }

    def _prepare_consumer_chart_data(self) -> Dict[str, Any]:
        """Prepare data for consumer charts"""
        names = [c.consumer_name for c in self.metrics.consumer_metrics]

        # Calculate throughput (messages per second)
        throughput = []
        for consumer in self.metrics.consumer_metrics:
            if consumer.end_time and consumer.processing_time > 0:
                tps = consumer.messages_received / consumer.processing_time
                throughput.append(tps)
            else:
                throughput.append(0)

        return {
            'names': names,
            'throughput': throughput
        }

    def _prepare_api_chart_data(self) -> Dict[str, Any]:
        """Prepare data for API charts"""
        endpoints = list(self.metrics.api_metrics.keys())
        requests = [m.total_requests for m in self.metrics.api_metrics.values()]

        # Aggregate retry distribution across all endpoints
        retry_distribution = {}
        error_types = {}

        for metrics in self.metrics.api_metrics.values():
            for retry_count, count in metrics.retry_distribution.items():
                retry_distribution[retry_count] = retry_distribution.get(retry_count, 0) + count

            for error_type, count in metrics.error_types.items():
                error_types[error_type] = error_types.get(error_type, 0) + count

        return {
            'endpoints': endpoints,
            'requests': requests,
            'retryLabels': list(retry_distribution.keys()),
            'retryCounts': list(retry_distribution.values()),
            'errorTypes': list(error_types.keys()),
            'errorCounts': list(error_types.values())
        }


# Global metrics collector instance
global_metrics_collector = MetricsCollector()


def create_report_at_exit(project_key: str, output_dir: Optional[Path] = None) -> str:
    """
    Create and save the HTML report at process exit.

    Args:
        project_key: JIRA project key
        output_dir: Directory to save the report (default: current directory)

    Returns:
        Path to the generated report file
    """
    global_metrics_collector.finalize()

    if output_dir is None:
        output_dir = Path.cwd()

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = output_dir / f"jira_processing_report_{project_key}_{timestamp}.html"

    generator = JiraProcessingReportGenerator(global_metrics_collector, project_key)
    report_path = generator.generate_html_report(output_path)

    return report_path


def track_producer_metrics(producer_name: str, jql_query: str,
                          total_records_processed: int, total_records_estimate: int,
                          processing_time: float, waiting_time: float, cpu_time: float,
                          start_time: datetime, end_time: datetime,
                          status: str = "completed", error_message: Optional[str] = None):
    """Track producer metrics"""
    difference_percentage = abs(total_records_processed - total_records_estimate) / total_records_estimate * 100 if total_records_estimate > 0 else 0

    metrics = ProducerMetrics(
        producer_name=producer_name,
        jql_query=jql_query,
        total_records_processed=total_records_processed,
        total_records_estimate=total_records_estimate,
        difference_percentage=difference_percentage,
        processing_time=processing_time,
        waiting_time=waiting_time,
        cpu_time=cpu_time,
        start_time=start_time,
        end_time=end_time,
        status=status,
        error_message=error_message
    )

    global_metrics_collector.add_producer_metrics(metrics)


def track_consumer_metrics(consumer_name: str, consumer_type: str,
                          messages_received: int, messages_sent: int,
                          processing_time: float, scaling_events: List[Dict[str, Any]],
                          start_time: datetime, end_time: Optional[datetime] = None,
                          status: str = "completed", error_message: Optional[str] = None):
    """Track consumer metrics"""
    metrics = ConsumerMetrics(
        consumer_name=consumer_name,
        consumer_type=consumer_type,
        messages_received=messages_received,
        messages_sent=messages_sent,
        processing_time=processing_time,
        scaling_events=scaling_events,
        start_time=start_time,
        end_time=end_time or datetime.now(),
        status=status,
        error_message=error_message
    )

    global_metrics_collector.add_consumer_metrics(metrics)


def track_api_call(endpoint: str, success: bool, retry_count: int,
                  time_excluding_wait: float, time_including_wait: float,
                  error_type: Optional[str] = None):
    """Track API call metrics"""
    global_metrics_collector.update_api_metrics(
        endpoint=endpoint,
        success=success,
        retry_count=retry_count,
        time_excluding_wait=time_excluding_wait,
        time_including_wait=time_including_wait,
        error_type=error_type
    )
