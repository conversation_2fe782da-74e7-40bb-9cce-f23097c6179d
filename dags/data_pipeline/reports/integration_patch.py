"""
Integration Patch for JIRA Processing Metrics

This module provides patches to integrate metrics tracking into existing
functions without major code restructuring.
"""

import time
from datetime import datetime
from typing import Dict, Any, Optional


from .jira_processing_report_generator import track_api_call


class FetchWithRetriesMetricsTracker:
    """Tracks metrics for fetch_with_retries function calls"""
    
    @staticmethod
    def extract_endpoint_from_url(url: str) -> str:
        """Extract meaningful endpoint identifier from URL"""
        if '/rest/api/' in url:
            # Extract the API endpoint part
            parts = url.split('/rest/api/')
            if len(parts) > 1:
                api_part = parts[1].split('?')[0]  # Remove query parameters
                return f"api/{api_part}"
        
        # Fallback to last part of URL
        return url.split('/')[-1].split('?')[0] if '/' in url else url
    
    @staticmethod
    def track_fetch_call(url: str, method: str, result: Dict[str, Any], 
                        start_time: float, end_time: float):
        """Track a fetch_with_retries call"""
        endpoint = FetchWithRetriesMetricsTracker.extract_endpoint_from_url(url)
        endpoint_with_method = f"{method} {endpoint}"
        
        success = result.get('success', False)
        
        # Extract retry information from metadata
        retry_count = 0
        error_type = None
        
        if 'metadata' in result and 'attempts' in result['metadata']:
            attempts = result['metadata']['attempts']
            retry_count = len(attempts) - 1 if attempts else 0
        
        if not success and 'exception' in result:
            exception_str = result['exception']
            # Extract error type from exception string
            if 'HTTP error' in exception_str:
                error_type = 'HTTP_ERROR'
            elif 'timeout' in exception_str.lower():
                error_type = 'TIMEOUT'
            elif 'connection' in exception_str.lower():
                error_type = 'CONNECTION_ERROR'
            else:
                error_type = 'OTHER'
        
        # Calculate timing (for now, assume no separate wait time tracking)
        total_time = end_time - start_time
        
        track_api_call(
            endpoint=endpoint_with_method,
            success=success,
            retry_count=retry_count,
            time_excluding_wait=total_time,  # TODO: Separate actual processing time
            time_including_wait=total_time,
            error_type=error_type
        )


def patch_fetch_with_retries():
    """
    Patch the fetch_with_retries function to include metrics tracking.
    This should be called at the start of process_jira_issues.
    """
    from dags.data_pipeline.jira import api_client
    
    # Store original function
    original_fetch_with_retries = api_client.fetch_with_retries
    
    async def enhanced_fetch_with_retries(*args, **kwargs):
        """Enhanced version with metrics tracking"""
        # Extract parameters
        method = args[1] if len(args) > 1 else kwargs.get('method', 'GET')
        url = args[2] if len(args) > 2 else kwargs.get('url', 'unknown')
        
        start_time = time.time()
        
        try:
            result = await original_fetch_with_retries(*args, **kwargs)
            end_time = time.time()
            
            # Track the call
            FetchWithRetriesMetricsTracker.track_fetch_call(
                url, method, result, start_time, end_time
            )
            
            return result
            
        except Exception as e:
            end_time = time.time()
            
            # Track failed call
            error_result = {
                'success': False,
                'exception': str(e),
                'metadata': {'attempts': []}
            }
            
            FetchWithRetriesMetricsTracker.track_fetch_call(
                url, method, error_result, start_time, end_time
            )
            
            raise
    
    # Replace the function
    api_client.fetch_with_retries = enhanced_fetch_with_retries
    
    return original_fetch_with_retries


class ProcessingMetricsCollector:
    """Collects metrics during JIRA processing"""
    
    def __init__(self):
        self.producer_stats = {}
        self.consumer_stats = {}
        self.start_time = datetime.now()
    
    def start_producer_tracking(self, producer_name: str, jql_query: str, 
                               total_estimate: int):
        """Start tracking a producer"""
        self.producer_stats[producer_name] = {
            'jql_query': jql_query,
            'total_estimate': total_estimate,
            'start_time': datetime.now(),
            'records_processed': 0,
            'cpu_start': time.process_time(),
            'wall_start': time.time()
        }
    
    def update_producer_progress(self, producer_name: str, records_processed: int):
        """Update producer progress"""
        if producer_name in self.producer_stats:
            self.producer_stats[producer_name]['records_processed'] = records_processed
    
    def finish_producer_tracking(self, producer_name: str, status: str = "completed",
                                error_message: Optional[str] = None):
        """Finish tracking a producer"""
        if producer_name not in self.producer_stats:
            return
        
        stats = self.producer_stats[producer_name]
        end_time = datetime.now()
        cpu_end = time.process_time()
        wall_end = time.time()
        
        from .jira_processing_report_generator import track_producer_metrics
        
        track_producer_metrics(
            producer_name=producer_name,
            jql_query=stats['jql_query'],
            total_records_processed=stats['records_processed'],
            total_records_estimate=stats['total_estimate'],
            processing_time=wall_end - stats['wall_start'],
            waiting_time=0.0,  # TODO: Track actual waiting time
            cpu_time=cpu_end - stats['cpu_start'],
            start_time=stats['start_time'],
            end_time=end_time,
            status=status,
            error_message=error_message
        )
    
    def start_consumer_tracking(self, consumer_name: str, consumer_type: str):
        """Start tracking a consumer"""
        self.consumer_stats[consumer_name] = {
            'consumer_type': consumer_type,
            'start_time': datetime.now(),
            'messages_received': 0,
            'messages_sent': 0,
            'scaling_events': []
        }
    
    def update_consumer_progress(self, consumer_name: str, messages_received: int = 0,
                               messages_sent: int = 0, scaling_event: Optional[Dict] = None):
        """Update consumer progress"""
        if consumer_name not in self.consumer_stats:
            return
        
        stats = self.consumer_stats[consumer_name]
        if messages_received > 0:
            stats['messages_received'] += messages_received
        if messages_sent > 0:
            stats['messages_sent'] += messages_sent
        if scaling_event:
            stats['scaling_events'].append(scaling_event)
    
    def finish_consumer_tracking(self, consumer_name: str, status: str = "completed",
                               error_message: Optional[str] = None):
        """Finish tracking a consumer"""
        if consumer_name not in self.consumer_stats:
            return
        
        stats = self.consumer_stats[consumer_name]
        end_time = datetime.now()
        
        from .jira_processing_report_generator import track_consumer_metrics
        
        track_consumer_metrics(
            consumer_name=consumer_name,
            consumer_type=stats['consumer_type'],
            messages_received=stats['messages_received'],
            messages_sent=stats['messages_sent'],
            processing_time=(end_time - stats['start_time']).total_seconds(),
            scaling_events=stats['scaling_events'],
            start_time=stats['start_time'],
            end_time=end_time,
            status=status,
            error_message=error_message
        )


# Global instance for easy access
global_processing_metrics = ProcessingMetricsCollector()


def setup_metrics_tracking(project_key: str):
    """
    Setup comprehensive metrics tracking for JIRA processing.
    Call this at the beginning of process_jira_issues.
    """
    from .metrics_integration import setup_exit_report_generation
    
    # Setup automatic report generation
    setup_exit_report_generation(project_key)
    
    # Patch fetch_with_retries
    original_fetch = patch_fetch_with_retries()
    
    return original_fetch


def add_producer_tracking_to_get_issues_from_jira_jql():
    """
    Add tracking to get_issues_from_jira_jql function.
    This should be called to wrap the existing function.
    """
    from dags.data_pipeline import utility_code
    
    original_function = utility_code.get_issues_from_jira_jql
    
    async def enhanced_get_issues_from_jira_jql(*args, **kwargs):
        """Enhanced version with producer tracking"""
        # Extract parameters
        project_key = args[0] if len(args) > 0 else kwargs.get('project_key', 'unknown')
        name = args[7] if len(args) > 7 else kwargs.get('name', 'unknown_producer')
        jql = args[8] if len(args) > 8 else kwargs.get('jql', '')
        total_records = args[9] if len(args) > 9 else kwargs.get('total_records', 0)
        
        # Start tracking
        global_processing_metrics.start_producer_tracking(name, jql, total_records)
        
        try:
            result = await original_function(*args, **kwargs)
            
            # TODO: Extract actual records processed from result
            # This would need to be implemented based on the actual function behavior
            
            global_processing_metrics.finish_producer_tracking(name, "completed")
            return result
            
        except Exception as e:
            global_processing_metrics.finish_producer_tracking(name, "failed", str(e))
            raise
    
    # Replace the function
    utility_code.get_issues_from_jira_jql = enhanced_get_issues_from_jira_jql
    
    return original_function


async def add_consumer_tracking_to_consume_functions():
    """
    Add tracking to consumer functions.
    This should be called to wrap the existing consumer functions.
    """
    from dags.data_pipeline import utility_code
    
    consumer_functions = [
        ('consume_issues', 'consume_issues'),
        ('consume_changelog', 'consume_changelog'),
        ('consume_worklog', 'consume_worklog'),
        ('consume_comment', 'consume_comment'),
        ('consume_issue_links', 'consume_issue_links'),
        ('consume_issue', 'consume_issue')
    ]
    
    original_functions = {}
    
    for func_name, consumer_type in consumer_functions:
        if hasattr(utility_code, func_name):
            original_func = getattr(utility_code, func_name)
            original_functions[func_name] = original_func
            
            async def create_enhanced_consumer(original, ctype):
                async def enhanced_consumer(*args, **kwargs):
                    # Extract consumer name from arguments
                    consumer_name = f"{ctype}_{args[0] if args else 'unknown'}"
                    
                    global_processing_metrics.start_consumer_tracking(consumer_name, ctype)
                    
                    try:
                        result = await original(*args, **kwargs)
                        global_processing_metrics.finish_consumer_tracking(consumer_name, "completed")
                        return result
                    except Exception as e:
                        global_processing_metrics.finish_consumer_tracking(consumer_name, "failed", str(e))
                        raise
                
                return enhanced_consumer
            
            enhanced_func = await create_enhanced_consumer(original_func, consumer_type)
            setattr(utility_code, func_name, enhanced_func)
    
    return original_functions
