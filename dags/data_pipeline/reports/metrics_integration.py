"""
Metrics Integration Module

Integrates reporting functionality with existing JIRA processing functions.
This module provides decorators and context managers to automatically track
metrics during processing.
"""

import time
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, Callable
from functools import wraps
from contextlib import asynccontextmanager
from logging import Logger

from .jira_processing_report_generator import (
    track_producer_metrics, track_consumer_metrics, track_api_call,
    create_report_at_exit, global_metrics_collector
)


class MetricsTracker:
    """Context manager for tracking processing metrics"""
    
    def __init__(self, component_name: str, component_type: str):
        self.component_name = component_name
        self.component_type = component_type
        self.start_time = None
        self.end_time = None
        self.metrics = {}
        
    def __enter__(self):
        self.start_time = datetime.now()
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = datetime.now()
        
        if self.component_type == "producer":
            track_producer_metrics(
                producer_name=self.component_name,
                jql_query=self.metrics.get('jql_query', ''),
                total_records_processed=self.metrics.get('total_records_processed', 0),
                total_records_estimate=self.metrics.get('total_records_estimate', 0),
                processing_time=self.metrics.get('processing_time', 0),
                waiting_time=self.metrics.get('waiting_time', 0),
                cpu_time=self.metrics.get('cpu_time', 0),
                start_time=self.start_time,
                end_time=self.end_time,
                status="failed" if exc_type else "completed",
                error_message=str(exc_val) if exc_val else None
            )
        elif self.component_type == "consumer":
            track_consumer_metrics(
                consumer_name=self.component_name,
                consumer_type=self.metrics.get('consumer_type', 'unknown'),
                messages_received=self.metrics.get('messages_received', 0),
                messages_sent=self.metrics.get('messages_sent', 0),
                processing_time=self.metrics.get('processing_time', 0),
                scaling_events=self.metrics.get('scaling_events', []),
                start_time=self.start_time,
                end_time=self.end_time,
                status="failed" if exc_type else "completed",
                error_message=str(exc_val) if exc_val else None
            )
    
    def update_metrics(self, **kwargs):
        """Update metrics during processing"""
        self.metrics.update(kwargs)


def track_api_calls(func: Callable) -> Callable:
    """Decorator to track API call metrics for fetch_with_retries"""
    
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # Extract URL from arguments
        url = args[2] if len(args) > 2 else kwargs.get('url', 'unknown')
        endpoint = url.split('/')[-1] if '/' in url else url
        
        start_time = time.time()
        retry_count = 0
        success = False
        error_type = None
        
        try:
            result = await func(*args, **kwargs)
            
            # Extract metrics from result
            if isinstance(result, dict):
                success = result.get('success', False)
                if 'metadata' in result and 'attempts' in result['metadata']:
                    retry_count = len(result['metadata']['attempts']) - 1
                if not success and 'exception' in result:
                    error_type = result.get('exception', '').split(':')[0]
            
            end_time = time.time()
            time_total = end_time - start_time
            
            # For now, assume waiting time is 0 (can be enhanced later)
            time_excluding_wait = time_total
            time_including_wait = time_total
            
            # Track the API call
            track_api_call(
                endpoint=endpoint,
                success=success,
                retry_count=retry_count,
                time_excluding_wait=time_excluding_wait,
                time_including_wait=time_including_wait,
                error_type=error_type
            )
            
            return result
            
        except Exception as e:
            end_time = time.time()
            time_total = end_time - start_time
            
            track_api_call(
                endpoint=endpoint,
                success=False,
                retry_count=retry_count,
                time_excluding_wait=time_total,
                time_including_wait=time_total,
                error_type=type(e).__name__
            )
            
            raise
    
    return wrapper


@asynccontextmanager
async def producer_metrics_context(producer_name: str, jql_query: str, 
                                 total_records_estimate: int):
    """Async context manager for tracking producer metrics"""
    start_time = datetime.now()
    cpu_start = time.process_time()
    wall_start = time.time()
    
    metrics = {
        'total_records_processed': 0,
        'waiting_time': 0.0
    }
    
    try:
        yield metrics
        status = "completed"
        error_message = None
    except Exception as e:
        status = "failed"
        error_message = str(e)
        raise
    finally:
        end_time = datetime.now()
        cpu_end = time.process_time()
        wall_end = time.time()
        
        processing_time = wall_end - wall_start
        cpu_time = cpu_end - cpu_start
        waiting_time = metrics.get('waiting_time', 0.0)
        
        track_producer_metrics(
            producer_name=producer_name,
            jql_query=jql_query,
            total_records_processed=metrics.get('total_records_processed', 0),
            total_records_estimate=total_records_estimate,
            processing_time=processing_time,
            waiting_time=waiting_time,
            cpu_time=cpu_time,
            start_time=start_time,
            end_time=end_time,
            status=status,
            error_message=error_message
        )


@asynccontextmanager
async def consumer_metrics_context(consumer_name: str, consumer_type: str):
    """Async context manager for tracking consumer metrics"""
    start_time = datetime.now()
    
    metrics = {
        'messages_received': 0,
        'messages_sent': 0,
        'scaling_events': []
    }
    
    try:
        yield metrics
        status = "completed"
        error_message = None
    except Exception as e:
        status = "failed"
        error_message = str(e)
        raise
    finally:
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        track_consumer_metrics(
            consumer_name=consumer_name,
            consumer_type=consumer_type,
            messages_received=metrics.get('messages_received', 0),
            messages_sent=metrics.get('messages_sent', 0),
            processing_time=processing_time,
            scaling_events=metrics.get('scaling_events', []),
            start_time=start_time,
            end_time=end_time,
            status=status,
            error_message=error_message
        )


def setup_exit_report_generation(project_key: str, output_dir: Optional[str] = None):
    """Setup automatic report generation at process exit"""
    import atexit
    from pathlib import Path
    
    output_path = Path(output_dir) if output_dir else Path.cwd()
    
    def generate_report():
        try:
            report_path = create_report_at_exit(project_key, output_path)
            print(f"📊 JIRA Processing Report generated: {report_path}")
        except Exception as e:
            print(f"❌ Failed to generate report: {e}")
    
    atexit.register(generate_report)


# Example usage functions for integration
async def enhanced_get_issues_from_jira_jql(project_key: str, producer_name: str, 
                                          jql_query: str, total_records_estimate: int,
                                          original_function: Callable, *args, **kwargs):
    """Enhanced version of get_issues_from_jira_jql with metrics tracking"""
    
    async with producer_metrics_context(producer_name, jql_query, total_records_estimate) as metrics:
        # Track waiting time if circuit breaker causes delays
        wait_start = time.time()
        
        # Call original function
        result = await original_function(*args, **kwargs)
        
        wait_end = time.time()
        
        # Update metrics (these would need to be extracted from the actual processing)
        metrics['total_records_processed'] = kwargs.get('total_records', 0)  # This needs to be updated based on actual implementation
        metrics['waiting_time'] = wait_end - wait_start
        
        return result


async def enhanced_consume_function(consumer_name: str, consumer_type: str,
                                  original_function: Callable, *args, **kwargs):
    """Enhanced consumer function with metrics tracking"""
    
    async with consumer_metrics_context(consumer_name, consumer_type) as metrics:
        # Track messages processed
        messages_received = 0
        messages_sent = 0
        scaling_events = []
        
        # This would need to be integrated with the actual queue processing logic
        result = await original_function(*args, **kwargs)
        
        # Update metrics based on actual processing
        metrics['messages_received'] = messages_received
        metrics['messages_sent'] = messages_sent
        metrics['scaling_events'] = scaling_events
        
        return result
